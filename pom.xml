<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yimlink.ymcloud</groupId>
        <artifactId>paybylink</artifactId>
        <version>2.3.0-SNAPSHOT</version>
    </parent>

    <groupId>com.burberry.paybylink</groupId>
    <artifactId>productservice</artifactId>
    <version>2.3.0-SNAPSHOT</version>
    <name>productservice</name>
    <description>productservice</description>

    <properties>
        <java.version>21</java.version>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <sonar.projectKey>wecom_paybylink_paybylink-product-service_AZQ6a9slCooZcnB2WGcU</sonar.projectKey>
        <sonar.projectName>paybylink-product-service</sonar.projectName>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yimlink.ymcloud.paybylink</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yimlink.ymcloud.paybylink</groupId>
            <artifactId>product</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.opensearch.client</groupId>
            <artifactId>opensearch-rest-high-level-client</artifactId>
        </dependency>
    </dependencies>

</project>
