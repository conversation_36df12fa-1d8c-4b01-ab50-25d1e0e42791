<?xml version="1.0" encoding="UTF-8"?>


<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <!--  <localRepository>/Users/<USER>/.m2/repository</localRepository>-->

    <servers>
		<server>
            <id>yimlinkapp-pay-by-link-linkioapp-paybylink</id>
            <username>ptnbel<PERSON>zrov</username>
            <password>5bcbe1125bd71b707654eac9ae4026d52af57c52</password>
        </server>
    </servers>


    <mirrors>
        <mirror>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>

        <mirror>
            <id>nexus</id>
            <name>internal nexus repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <mirrorOf>central</mirrorOf>
        </mirror>

        <mirror>
            <id>yimlinkapp-pay-by-link-linkioapp-paybylink</id>
		    <name>linkioapp-paybylink</name>
		    <url>https://yimlinkapp-maven.pkg.coding.net/repository/pay-by-link/linkioapp-paybylink/</url>
			<mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>


    <profiles>
        <profile>
            <id>Repository Proxy</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
				<repository>
                    <id>yimlinkapp-pay-by-link-linkioapp-paybylink</id>
					<name>linkioapp-paybylink</name>
                    <url>https://yimlinkapp-maven.pkg.coding.net/repository/pay-by-link/linkioapp-paybylink/</url>
                    <snapshots>
                        <enabled>true</enabled>
                        <updatePolicy>always</updatePolicy>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>
</settings>
