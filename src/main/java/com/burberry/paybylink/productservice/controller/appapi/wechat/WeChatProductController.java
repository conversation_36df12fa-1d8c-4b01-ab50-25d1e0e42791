package com.burberry.paybylink.productservice.controller.appapi.wechat;

import com.burberry.paybylink.productservice.model.req.ProductListReq;
import com.burberry.paybylink.productservice.model.vo.TrackingProductSearchReq;
import com.github.pagehelper.PageInfo;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.auth.annotation.WeChatAPI;
import com.yimlink.ymcloud.paybylink.common.model.dto.OutletProductPriceDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductSpuDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.auth.WeChatUserDTO;
import com.yimlink.ymcloud.paybylink.common.model.enums.PEOPLE_TYPE_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.enums.REFERENCE_TYPE_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.enums.TERMINAL_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.vo.ProductVO;
import com.yimlink.ymcloud.paybylink.common.model.vo.TrackingProductVO;
import com.yimlink.ymcloud.paybylink.common.service.ITrackingRecordService;
import com.yimlink.ymcloud.paybylink.product.service.ProductBusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/product-api/app-api/wechat/product/")
@Tag(name = "WeChat Product APIs")
public class WeChatProductController {

    @Resource
    private ProductBusService productBusService;

    @Resource
    private ITrackingRecordService trackingRecordService;

    @Operation(summary = "get product list")
    @PostMapping(value = "/productList")
    @WeChatAPI
    public ApiResponse<List<ProductVO>> productList(@RequestAttribute WeChatUserDTO weChatUserDTO,
                                                    @RequestBody @Valid ProductListReq req) {

        return ApiResponse.success(productBusService.findSpuList(req.getStoreId(), req.getProductIds(), PEOPLE_TYPE_ENUM.CUSTOMER, weChatUserDTO.getUnionid()));
    }

    @Operation(summary = "query product detail")
    @PostMapping(value = "/productDetail/{productId}")
    @WeChatAPI
    public ApiResponse<ProductSpuDTO> productDetail(@RequestAttribute WeChatUserDTO weChatUserDTO,
                                                    @PathVariable String productId) {

        return ApiResponse.success(productBusService.findSpuBySpu(productId, PEOPLE_TYPE_ENUM.CUSTOMER, weChatUserDTO.getUnionid()));
    }

    @Operation(summary = "query outlet sku price")
    @PostMapping(value = "/outletSkuPrice/{skuId}")
    @WeChatAPI
    public ApiResponse<OutletProductPriceDTO> outletSkuPrice(@RequestAttribute WeChatUserDTO weChatUserDTO,
                                                             @PathVariable String skuId) {

        return ApiResponse.success(productBusService.queryOutletProductPriceBySku(skuId));
    }

    @Operation(summary = "selectTrackingProducts")
    @PostMapping(value = "/selectTrackingProducts")
    @WeChatAPI
    public ApiResponse<PageInfo<TrackingProductVO>> selectTrackingProducts(@RequestAttribute WeChatUserDTO weChatUserDTO,
                                                                           @RequestBody @Valid TrackingProductSearchReq req) {
        return ApiResponse.success(trackingRecordService.selectTrackingProducts(req.getPageNum(), req.getPageSize(), TERMINAL_ENUM.WECHAT, weChatUserDTO.getUnionid(), REFERENCE_TYPE_ENUM.PRODUCT));
    }

}
