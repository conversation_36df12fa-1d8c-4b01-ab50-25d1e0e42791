package com.burberry.paybylink.productservice.controller.appapi.wechat;

import com.burberry.paybylink.productservice.model.vo.WishCreatVo;
import com.burberry.paybylink.productservice.model.vo.WishProductSearchReq;
import com.burberry.paybylink.productservice.model.vo.WishUpdVo;
import com.burberry.paybylink.productservice.service.WishProductService;
import com.github.pagehelper.PageInfo;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.auth.annotation.WeChatAPI;
import com.yimlink.ymcloud.paybylink.common.model.dto.auth.WeChatUserDTO;
import com.yimlink.ymcloud.paybylink.common.model.enums.PEOPLE_TYPE_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.enums.STOCK_TYPE_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.vo.WishProductVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/product-api/app-api/wechat/wish/")
@Tag(name = "WeChat Wish APIs")
public class WeChatWishController {

    @Resource
    private WishProductService wishProductService;


    @Operation(summary = "addWish")
    @PostMapping(value = "/addWish")
    @WeChatAPI
    public ApiResponse<Void> addWish(@RequestAttribute WeChatUserDTO weChatUserDTO,
                               @RequestBody @Valid WishCreatVo createVo) {
        wishProductService.addWish(PEOPLE_TYPE_ENUM.CUSTOMER, weChatUserDTO.getUnionid(), createVo);
        return ApiResponse.success();
    }


    @Operation(summary = "selectWishProductList")
    @PostMapping(value = "/selectWishProductList")
    @WeChatAPI
    public ApiResponse<PageInfo<WishProductVO>> selectWishProductList(@RequestAttribute WeChatUserDTO weChatUserDTO,
                                                                      @RequestBody @Valid WishProductSearchReq req) {
        return ApiResponse.success(wishProductService.selectWishProductList(PEOPLE_TYPE_ENUM.CUSTOMER, weChatUserDTO.getUnionid(), req));
    }


    @Operation(summary = "updWish")
    @PostMapping(value = "/updWish")
    @WeChatAPI
    public ApiResponse<Void> updWish(@RequestAttribute WeChatUserDTO weChatUserDTO,
                               @RequestBody @Valid WishUpdVo updVo) {
        updVo.setStockType(STOCK_TYPE_ENUM.STORE);
        wishProductService.updWish(PEOPLE_TYPE_ENUM.CUSTOMER, weChatUserDTO.getUnionid(), updVo);
        return ApiResponse.success();
    }
}
