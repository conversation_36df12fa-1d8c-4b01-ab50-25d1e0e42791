package com.burberry.paybylink.productservice.controller.appapi.wecom;

import com.burberry.paybylink.productservice.model.dto.ec.EcCartShareDTO;
import com.burberry.paybylink.productservice.model.dto.ec.EcProductListDTO;
import com.burberry.paybylink.productservice.model.dto.ec.EcProductShareDTO;
import com.burberry.paybylink.productservice.model.vo.ECProductItemsVo;
import com.burberry.paybylink.productservice.model.vo.EcShareVo;
import com.burberry.paybylink.productservice.service.ECProductService;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.auth.annotation.WeComAPI;
import com.yimlink.ymcloud.paybylink.common.model.dto.auth.WeComUserDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/19 16:39
 */
@RestController
@RequestMapping("/product-api/app-api/wecom/ec-product")
@Tag(name = "Product APIs")
public class WeComECProductController {

    @Resource
    private ECProductService ecProductService;

    @Operation(summary = "query ec stock by spu code")
    @GetMapping(value = "/getEcStock")
    @WeComAPI
    public ApiResponse<ECProductItemsVo> getEcStock(@RequestParam @Schema(description = "product spu", requiredMode = Schema.RequiredMode.REQUIRED, example = "spu123") String code) {
        return ApiResponse.success(ecProductService.getEcStock(code));
    }

    @Operation(summary = "query ec stock by spu code list")
    @PostMapping(value = "/getEcStocks")
    @WeComAPI
    public ApiResponse<List<ECProductItemsVo>> getEcStocks(@Validated @RequestBody EcProductListDTO dto) {
        return ApiResponse.success(ecProductService.getEcStocks(dto.getCodes()));
    }

    @Operation(summary = "ec product share or download")
    @PostMapping(value = "/productShare")
    @WeComAPI
    public ApiResponse<EcShareVo> productShare(@RequestAttribute WeComUserDTO weComUserDTO,
                                            @Validated @RequestBody EcProductShareDTO dto) throws UnsupportedEncodingException {
        return ApiResponse.success(ecProductService.productShare(weComUserDTO, dto));
    }

    @Operation(summary = "more ec products are shared from the shopping cart")
    @PostMapping(value = "/cartShare")
    @WeComAPI
    public ApiResponse<EcShareVo> cartShare(@RequestAttribute WeComUserDTO weComUserDTO,
                                            @Validated @RequestBody EcCartShareDTO dto) throws UnsupportedEncodingException {
        return ApiResponse.success(ecProductService.cartShare(weComUserDTO, dto));
    }
}
