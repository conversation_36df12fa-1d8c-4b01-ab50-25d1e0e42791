package com.burberry.paybylink.productservice.controller.appapi.wecom;

import com.burberry.paybylink.productservice.model.req.ProductListReq;
import com.burberry.paybylink.productservice.model.req.wecom.SearchLogListReq;
import com.burberry.paybylink.productservice.model.req.wecom.SkuStockReq;
import com.burberry.paybylink.productservice.model.req.wecom.StoreStockReq;
import com.burberry.paybylink.productservice.model.vo.ProductVo;
import com.burberry.paybylink.productservice.opensearch.OpenSearchService;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.auth.annotation.WeComAPI;
import com.yimlink.ymcloud.paybylink.common.model.dto.OutletProductPriceDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductSpuDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.StoreStockDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.auth.WeComUserDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.look.LookOpenSearchResultDTO;
import com.yimlink.ymcloud.paybylink.common.model.enums.PEOPLE_TYPE_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.po.ColorProductPO;
import com.yimlink.ymcloud.paybylink.common.model.po.SearchLogPO;
import com.yimlink.ymcloud.paybylink.common.model.po.TagPO;
import com.yimlink.ymcloud.paybylink.common.model.vo.ProductVO;
import com.yimlink.ymcloud.paybylink.common.model.vo.StoreVO;
import com.yimlink.ymcloud.paybylink.common.util.BeanCopyUtil;
import com.yimlink.ymcloud.paybylink.product.model.dto.ElasticRelatedWordListDTO;
import com.yimlink.ymcloud.paybylink.product.model.dto.StoreStockAllDTO;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.ProductOpenSearchResult;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.SearchLookDTO;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.SearchProductDTO;
import com.yimlink.ymcloud.paybylink.product.model.vo.CategoryVO;
import com.yimlink.ymcloud.paybylink.product.service.ProductBusService;
import com.yimlink.ymcloud.paybylink.product.service.SearchLogBusService;
import com.yimlink.ymcloud.paybylink.product.service.TagBusService;
import com.yimlink.ymcloud.paybylink.product.service.elastic.ElasticRelatedWordBusService;
import com.yimlink.ymcloud.paybylink.product.service.wecom.WeComProductBusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/product-api/app-api/wecom/product/")
@Tag(name = "WeCom Product APIs")
public class WeComProductController {

    @Resource
    private ProductBusService productBusService;

    @Resource
    private WeComProductBusService weComProductBusService;

    @Resource
    private OpenSearchService openSearchService;

    @Resource
    private SearchLogBusService searchLogBusService;

    @Resource
    private ElasticRelatedWordBusService elasticRelatedWordService;

    @Resource
    private TagBusService tagBusService;


    @Operation(summary = "query product by sku")
    @GetMapping(value = "/getBySKU")
    @WeComAPI
    public ApiResponse<ProductVo> getBySKU(@RequestParam @Schema(description = "product sku", requiredMode = Schema.RequiredMode.REQUIRED, example = "sku123") String productSku) {
        ProductDTO productDTO = productBusService.findBySku(productSku);
        return ApiResponse.success(BeanCopyUtil.copyFrom(productDTO, ProductVo.class));
    }

    @Operation(summary = "query product by ean")
    @GetMapping(value = "/getByEAN")
    @WeComAPI
    public ApiResponse<ProductVo> getByEAN(@RequestParam @Schema(description = "product ean", requiredMode = Schema.RequiredMode.REQUIRED, example = "ean123") String ean) {
        ProductDTO productDTO = productBusService.findByEan(ean);
        return ApiResponse.success(BeanCopyUtil.copyFrom(productDTO, ProductVo.class));
    }

    @Operation(summary = "query category")
    @PostMapping(value = "/getCategoryVO/{categoryId}")
    @WeComAPI
    public ApiResponse<CategoryVO> getCategoryVO(
            @PathVariable @Schema(description = "root categoryId is 0", example = "0") String categoryId,
            @RequestAttribute WeComUserDTO weComUserDTO) {
        return ApiResponse.success(weComProductBusService.getCategoryVO(categoryId, weComUserDTO));
    }

    @Operation(summary = "get tag list")
    @PostMapping(value = "/getTagList")
    @WeComAPI
    public ApiResponse<List<TagPO>> getTagList(@RequestAttribute WeComUserDTO weComUserDTO) {
        return ApiResponse.success(tagBusService.getHotTagList());
    }


    @Operation(summary = "search product")
    @PostMapping(value = "/searchProduct")
    @WeComAPI
    public ApiResponse<ProductOpenSearchResult> searchProduct(@RequestAttribute WeComUserDTO weComUserDTO,
                                                              @RequestBody @Valid SearchProductDTO req) {
        return ApiResponse.success(productBusService.searchProduct(req, weComUserDTO));
    }

    @Operation(summary = "search look")
    @PostMapping(value = "/searchLook")
    @WeComAPI
    public ApiResponse<LookOpenSearchResultDTO> searchLook(@RequestAttribute WeComUserDTO weComUserDTO,
                                                           @RequestBody @Valid SearchLookDTO req) {

        return ApiResponse.success(productBusService.searchLook(req, weComUserDTO));
    }


    @Operation(summary = "get search log list")
    @PostMapping(value = "/searchLogList")
    @WeComAPI
    public ApiResponse<List<SearchLogPO>> searchLogList(@RequestAttribute WeComUserDTO weComUserDTO,
                                                        @RequestBody @Valid SearchLogListReq req) {

        return ApiResponse.success(searchLogBusService.getSearchLogList(weComUserDTO, req.getStoreId(), req.getKeyword()));

    }

    @Operation(summary = "clean search log")
    @PostMapping(value = "/cleanSearchLog/{storeId}")
    @WeComAPI
    public ApiResponse<Void> cleanSearchLog(@RequestAttribute WeComUserDTO weComUserDTO,
                                            @PathVariable Integer storeId) {
        searchLogBusService.cleanSearchLog(weComUserDTO, storeId);
        return ApiResponse.success();

    }

    @Operation(summary = "get product list")
    @PostMapping(value = "/productList")
    @WeComAPI
    public ApiResponse<List<ProductVO>> productList(@RequestAttribute WeComUserDTO weComUserDTO,
                                                    @RequestBody @Valid ProductListReq req) {

        return ApiResponse.success(productBusService.findSpuList(req.getStoreId(), req.getProductIds(), PEOPLE_TYPE_ENUM.CA, weComUserDTO.getUserid()));
    }

    @Operation(summary = "query product detail")
    @PostMapping(value = "/productDetail/{productId}")
    @WeComAPI
    public ApiResponse<ProductSpuDTO> productDetail(@RequestAttribute WeComUserDTO weComUserDTO,
                                                    @PathVariable String productId) {

        return ApiResponse.success(productBusService.findSpuBySpu(productId, PEOPLE_TYPE_ENUM.CA, weComUserDTO.getUserid()));
    }

    @Operation(summary = "query store sku list stock")
    @PostMapping(value = "/skuListStock")
    @WeComAPI
    public ApiResponse<List<StoreStockDTO>> skuListStock(@RequestAttribute WeComUserDTO weComUserDTO,
                                                               @RequestBody @Valid StoreStockReq storeStockReq) {

        return ApiResponse.success(productBusService.getSkuListStockByLocation(storeStockReq.getSkuList(),
                storeStockReq.getStoreId()));
    }

    @Operation(summary = "query sku store list stock")
    @PostMapping(value = "/storeListStock")
    @WeComAPI
    public ApiResponse<List<StoreStockDTO>> storeListStock(@RequestAttribute WeComUserDTO weComUserDTO,
                                                           @RequestBody @Valid SkuStockReq skuStockReq) {

        return ApiResponse.success(productBusService.getUserStoreStockBySku(skuStockReq.getSku(), skuStockReq.getStoreId()));
    }

    @Operation(summary = "query outlet store distance list")
    @PostMapping(value = "/outletStoreDistanceList/{storeId}")
    @WeComAPI
    public ApiResponse<List<StoreVO>> outletStoreList(@RequestAttribute WeComUserDTO weComUserDTO,
                                                      @PathVariable Integer storeId) {

        return ApiResponse.success(productBusService.getOtherOutletStoreDistanceList(storeId));
    }

    @Operation(summary = "query outlet sku price")
    @PostMapping(value = "/outletSkuPrice/{skuId}")
    @WeComAPI
    public ApiResponse<OutletProductPriceDTO> outletSkuPrice(@RequestAttribute WeComUserDTO weComUserDTO,
                                                             @PathVariable String skuId) {

        return ApiResponse.success(productBusService.queryOutletProductPriceBySku(skuId));
    }

    @Operation(summary = "query sku store all stock")
    @PostMapping(value = "/storeListStockAll")
    @WeComAPI
    public ApiResponse<List<StoreStockAllDTO>> storeListStockAll(@RequestAttribute WeComUserDTO weComUserDTO,
                                                                 @RequestBody @Valid StoreStockReq req) {

        return ApiResponse.success(productBusService.getUserStoreStockBySkuAll(req.getStoreId(), req.getSkuList()));
    }

    @Operation(summary = "query cpu sku store all stock by cup")
    @PostMapping(value = "/storeListStockAllByCup")
    @WeComAPI
    public ApiResponse<List<StoreStockAllDTO>> storeListStockAllByCup(@RequestAttribute WeComUserDTO weComUserDTO,
                                                                      @RequestBody @Valid StoreStockReq req) {

        return ApiResponse.success(productBusService.getUserStoreStockBySkuAllByCup(req.getStoreId(), req.getSkuList()));
    }

    @Operation(summary = "get color list")
    @PostMapping(value = "/colorList")
    @WeComAPI
    public ApiResponse<List<ColorProductPO>> colorList(@RequestAttribute WeComUserDTO weComUserDTO) {
        return ApiResponse.success(weComProductBusService.getColorList());
    }

    @Operation(summary = "get elastic related word list")
    @PostMapping(value = "/relatedWordList")
    @WeComAPI
    public ApiResponse<List<String>> relatedWordList(@RequestAttribute WeComUserDTO weComUserDTO,
                                                     @RequestBody @Valid ElasticRelatedWordListDTO dto) {
        return ApiResponse.success(elasticRelatedWordService.getRelatedWordList(dto));
    }

}
