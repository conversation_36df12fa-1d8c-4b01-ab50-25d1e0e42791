package com.burberry.paybylink.productservice.controller.innerapi.product;

import com.burberry.paybylink.productservice.model.vo.ProductVo;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.inner.annotation.InnerAPI;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductDTO;
import com.yimlink.ymcloud.paybylink.common.util.BeanCopyUtil;
import com.yimlink.ymcloud.paybylink.product.service.ProductBusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/product-api/inner-api/product/")
@Tag(name = "Inner APIs")
public class InnerProductController {

    @Resource
    private ProductBusService productBusService;

    @Operation(summary = "query product by sku")
    @GetMapping(value = "/v1/getBySKU")
    @InnerAPI
    public ApiResponse<ProductVo> getBySKU(@RequestParam @Schema(description = "product sku",requiredMode =Schema.RequiredMode.REQUIRED,example = "sku123") String productSku) {
        ProductDTO productDTO = productBusService.findBySku(productSku);
        return ApiResponse.success(BeanCopyUtil.copyFrom(productDTO, ProductVo.class));
    }

}
