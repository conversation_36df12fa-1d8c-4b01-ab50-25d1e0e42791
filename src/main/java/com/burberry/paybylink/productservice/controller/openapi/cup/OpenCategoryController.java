package com.burberry.paybylink.productservice.controller.openapi.cup;

import com.burberry.paybylink.productservice.model.dto.cup.CupCategoryRequestDTO;
import com.burberry.paybylink.productservice.model.dto.cup.CupRequestDTO;
import com.burberry.paybylink.productservice.service.CupCategoryService;
import com.yimlink.ymcloud.paybylink.common.api.base.OpenApiResponse;
import com.yimlink.ymcloud.paybylink.common.open.annotation.OpenAPIs;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/product-api/open-api/cup/")
@Tag(name = "Open APIs")
public class OpenCategoryController {

    @Resource
    CupCategoryService cupCategoryService;

    @Operation(description = "category Sync")
    @PostMapping("/v1/categories")
    @OpenAPIs
    public OpenApiResponse<Void> categorySync(HttpServletRequest request, HttpServletResponse response,
                                              @Valid @RequestBody CupRequestDTO<CupCategoryRequestDTO> req) {
        try {
            cupCategoryService.categorySync(req);
            return OpenApiResponse.success();
        } catch (Exception e) {
            log.error("categorySync error", e);
            throw e;
        }
    }



}
