package com.burberry.paybylink.productservice.controller.openapi.cup;

import com.burberry.paybylink.productservice.model.req.open.ProductReq;
import com.burberry.paybylink.productservice.service.OpenProductService;
import com.yimlink.ymcloud.paybylink.common.api.base.OpenApiResponse;
import com.yimlink.ymcloud.paybylink.common.open.annotation.OpenAPIs;
import com.yimlink.ymcloud.paybylink.common.util.RedisUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/product-api/open-api/cup/")
@Tag(name = "Open APIs")
public class OpenProductController {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private OpenProductService openProductService;


    @Operation(description = "Push product")
    @PostMapping("/v1/products")
    @OpenAPIs
    public OpenApiResponse<Void> pushProduct(HttpServletRequest request, HttpServletResponse response,
                                             @Valid @RequestBody ProductReq req) throws IOException {
        String path = request.getRequestURI();
        try {
            String common = "Common";
            String errorMsg = "Request cannot be completed due to an error";
            OpenApiResponse<Void> openApiResponse = new OpenApiResponse<>();

            String key = "product-api:open-api:v1:products:" + req.getRequestId();
            String lockKey = "lock:" + key;
            boolean suc = redisUtils.setIfAbsentEx(lockKey, lockKey, 1L, TimeUnit.DAYS);
            if (!suc) {
                openApiResponse.addError(common, "duplicate request");
            }
            if (!CollectionUtils.isEmpty(openApiResponse.getErrors())) {
                log.error("errMsg: %s, requestId: %s".formatted(openApiResponse.getErrors(), req.getRequestId()));

                response.setStatus(400);
                openApiResponse.setTraceId(UUID.randomUUID().toString());
                openApiResponse.setMessage(errorMsg);
                openApiResponse.setStatusCode(400);
                openApiResponse.setTimestamp(new Date());
                openApiResponse.setPath(path);
                return openApiResponse;
            }

            openProductService.dealProduct(req);

            return OpenApiResponse.success();
        } catch (Exception e) {
            log.error("push product error", e);
            throw e;
        }
    }

}
