package com.burberry.paybylink.productservice.controller.openapi.health;

import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.auth.annotation.NotLogin;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/product-api/open-api/health")
@Tag(name = "Health Check")
public class HealthController {


    @GetMapping("/check")
    @NotLogin
    public ApiResponse<String> check() {
        return ApiResponse.success("OK");
    }


}
