package com.burberry.paybylink.productservice.opensearch;

import com.alibaba.fastjson.JSON;
import com.burberry.paybylink.productservice.model.req.open.PRODUCT_TAG_ENUM;
import com.yimlink.ymcloud.paybylink.common.exception.ServiceException;
import com.yimlink.ymcloud.paybylink.common.model.po.ElasticWeightPO;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.*;
import com.yimlink.ymcloud.paybylink.product.service.elastic.ElasticWeightBusService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.opensearch.action.admin.indices.delete.DeleteIndexRequest;
import org.opensearch.action.bulk.BulkRequest;
import org.opensearch.action.bulk.BulkResponse;
import org.opensearch.action.delete.DeleteResponse;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.action.index.IndexResponse;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.indices.CreateIndexRequest;
import org.opensearch.client.indices.CreateIndexResponse;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.client.indices.PutMappingRequest;
import org.opensearch.common.xcontent.XContentType;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.MultiMatchQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.aggregations.AggregationBuilders;
import org.opensearch.search.aggregations.bucket.nested.ParsedNested;
import org.opensearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.opensearch.search.aggregations.bucket.terms.Terms;
import org.opensearch.search.aggregations.metrics.ParsedMax;
import org.opensearch.search.aggregations.metrics.ParsedMin;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortBuilders;
import org.opensearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class OpenSearchService {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private ElasticWeightBusService elasticWeightService;

    private final String productSearchIndex = "product_search";

    public void createProductIndex() throws IOException {
        CreateIndexRequest request = new CreateIndexRequest(productSearchIndex);
        String settingsJson = OpenSearchIndexHelper.generateSettingsNode().toPrettyString();
        request.settings(settingsJson, XContentType.JSON);

        String mappingJson = OpenSearchIndexHelper.generateMappingNode(ProductOpenSearchDTO.class).toPrettyString();
        request.mapping(mappingJson, XContentType.JSON);

        CreateIndexResponse response = restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
        if (!response.isAcknowledged()) {
            throw new ServerException("索引创建失败: " + productSearchIndex);
        }
        log.info("索引创建成功: " + productSearchIndex);
    }

    public void updateProductIndex() throws IOException {
        PutMappingRequest request = new PutMappingRequest(productSearchIndex);
        String mappingJson = OpenSearchIndexHelper.generateMappingNode(ProductOpenSearchDTO.class).toPrettyString();
        request.source(mappingJson, XContentType.JSON);

        restHighLevelClient.indices().putMapping(request, RequestOptions.DEFAULT);
        log.info("索引映射更新成功: " + productSearchIndex);
    }

    public void deleteIndex() throws IOException {
        if (!indexExists()) {
            log.info("索引不存在: " + productSearchIndex);
            return;
        }

        DeleteIndexRequest request = new DeleteIndexRequest(productSearchIndex);

        restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
        log.info("索引删除成功: " + productSearchIndex);
    }

    private boolean indexExists() throws IOException {
        GetIndexRequest request = new GetIndexRequest(productSearchIndex);
        return restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
    }

    public IndexResponse saveProduct(ProductOpenSearchDTO productDTO) throws IOException {
        IndexRequest request = new IndexRequest(productSearchIndex).id(productDTO.getId()).source(JSON.toJSONString(productDTO), XContentType.JSON);
        return restHighLevelClient.index(request, RequestOptions.DEFAULT);
    }

    public DeleteResponse deleteProduct(String key) throws IOException {
        return restHighLevelClient.delete(new org.opensearch.action.delete.DeleteRequest(productSearchIndex, key), RequestOptions.DEFAULT);
    }

    public BulkResponse saveProducts(List<ProductOpenSearchDTO> productDTOS) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        for (ProductOpenSearchDTO productDTO : productDTOS) {
            IndexRequest request = new IndexRequest(productSearchIndex).id(productDTO.getId()).source(JSON.toJSONString(productDTO), XContentType.JSON);
            bulkRequest.add(request);
        }
        return restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
    }

    public BulkResponse deleteProducts(List<String> keys) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        for (String key : keys) {
            org.opensearch.action.delete.DeleteRequest deleteRequest = new org.opensearch.action.delete.DeleteRequest(productSearchIndex, key);
            bulkRequest.add(deleteRequest);
        }
        return restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
    }

    private final String sortStoreStock = "storeStock";
    private final String sortEcStock = "ecStock";
    private final String sortOtherStoreStock = "otherStoreStock";
    private final String startDate = "startDate";

    private final String aggregationTags = "tags";
    private final String aggregationTagsName = "tags.name";

    private final String aggregationPrice = "price";

    private final String aggregationColor = "color";
    private final String aggregationColorImage = "colorImage";

    private final String aggregationCategory = "categories";
    private final String aggregationCategoriesName = "categories.name";
    private final String aggregationCategoriesId = "categories.id";
    private final String aggregationCategoriesParentId = "categories.parentId";

    private final String aggregationLooks = "looks";
    private final String aggregationLooksId = "looks.id";
    private final String aggregationLooksType = "looks.lookType";
    private final String aggregationSeries = "looks.series";
    private final String aggregationSeriesName = "looks.series.name";
    private final String aggregationSeriesId = "looks.series.id";

    private final String aggregationHasLook = "hasLook";

    private final String aggregationReportingColorCode="reportingColorCode";
    private final String aggregationReportingColorName="reportingColorName";
    private final String aggregationReportingColorUrl="reportingColorUrl";

    private final String aggregationSpec="spec";
    private final String aggregationSpecUrl="specUrl";


    public ProductOpenSearchResult searchProduct(ProductOpenSearchRequest req) {

        SearchRequest searchRequest = new SearchRequest(productSearchIndex);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("storeCode", req.getStoreCode()))
                .must(QueryBuilders.matchQuery("type", req.getProductType().name()))
                .must(QueryBuilders.rangeQuery("startDate").lte(LocalDateTime.now()));

        boolQuery = buildQuery(boolQuery, req);

        searchSourceBuilder.query(boolQuery);

        searchSourceBuilder.aggregation(AggregationBuilders.terms(aggregationColor).field(aggregationColor).subAggregation(
                AggregationBuilders.terms(aggregationColorImage).field(aggregationColorImage)
        ));
        searchSourceBuilder.aggregation(
                AggregationBuilders.nested("nested_tags", aggregationTags)
                        .subAggregation(
                                AggregationBuilders.terms("tag_name").field(aggregationTagsName)
                        )
        );
        searchSourceBuilder.aggregation(AggregationBuilders.max("max_price").field(aggregationPrice));
        searchSourceBuilder.aggregation(AggregationBuilders.min("min_price").field(aggregationPrice));
        searchSourceBuilder.aggregation(
                AggregationBuilders.nested("nested_categories", aggregationCategory)
                        .subAggregation(
                                AggregationBuilders.terms("category_id").field(aggregationCategoriesId)
                                        .subAggregation(AggregationBuilders.terms("category_name").field(aggregationCategoriesName))
                                        .subAggregation(AggregationBuilders.terms("category_parentId").field(aggregationCategoriesParentId))
                        )
        );
        searchSourceBuilder.aggregation(
                AggregationBuilders.nested("nested_looks", aggregationLooks)
                        .subAggregation(
                                AggregationBuilders.terms("look_id").field(aggregationLooksId)
                        )
        );
        searchSourceBuilder.aggregation(
                AggregationBuilders.nested("nested_series", aggregationSeries)
                        .subAggregation(
                                AggregationBuilders.terms("series_id").field(aggregationSeriesId)
                                        .subAggregation(AggregationBuilders.terms("series_name").field(aggregationSeriesName))
                        )
        );

        //aggregation reporting_color
        searchSourceBuilder.aggregation(
                AggregationBuilders.terms("reporting_color_code").field(aggregationReportingColorCode)
                        .subAggregation(AggregationBuilders.terms("reporting_color_name").field(aggregationReportingColorName))
                        .subAggregation(AggregationBuilders.terms("reporting_color_url").field(aggregationReportingColorUrl))
        );

        //aggregation spec
        searchSourceBuilder.aggregation(
                AggregationBuilders.terms("spec").field(aggregationSpec).subAggregation(AggregationBuilders.terms("spec_url").field(aggregationSpecUrl))
        );



        if (StringUtils.isNotEmpty(req.getSortField()) && StringUtils.isNotEmpty(req.getSortOrder())) {
            SortOrder order = SortOrder.DESC;
            if ("ASC".equalsIgnoreCase(req.getSortOrder())) {
                order = SortOrder.ASC;
            }
            searchSourceBuilder
                    .sort(req.getSortField(), order)
                    .sort(SortBuilders.scoreSort())
                    .sort(sortStoreStock, SortOrder.DESC)
                    .sort(sortOtherStoreStock, SortOrder.DESC)
                    .sort(sortEcStock, SortOrder.DESC);
            if (!req.getSortField().equals(startDate)) {
                searchSourceBuilder.sort(startDate, SortOrder.DESC);
            }

        } else {
            searchSourceBuilder
                    .sort(SortBuilders.scoreSort())
                    .sort(sortStoreStock, SortOrder.DESC)
                    .sort(sortOtherStoreStock, SortOrder.DESC)
                    .sort(sortEcStock, SortOrder.DESC)
                    .sort(startDate, SortOrder.DESC);
        }

        searchRequest.source(searchSourceBuilder);

        searchSourceBuilder.size(req.getPageSize()).from(req.getFromIndex());

        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("OpenSearch搜索失败", e);
            throw new ServiceException("搜索失败");
        }

        if (searchResponse == null) {
            throw new ServiceException("搜索失败");
        }

        List<ProductOpenSearchDTO> datas = new ArrayList<>();
        int total = 0;
        int page = req.getFromIndex() / req.getPageSize() + 1;
        int totalPage = 0;
        int next = -1;
        if (searchResponse.getHits() != null && searchResponse.getHits().getTotalHits() != null) {
            datas = Arrays.stream(searchResponse.getHits().getHits()).map(h -> {
                String sourceAsString = h.getSourceAsString();
                return JSON.parseObject(sourceAsString, ProductOpenSearchDTO.class);
            }).toList();

            total = (int) searchResponse.getHits().getTotalHits().value;
            totalPage = (int) Math.ceil((double) total / req.getPageSize());
            if (totalPage > page) {
                next = req.getFromIndex() + req.getPageSize();
            }
        }

        ProductOpenSearchResult.Aggregation aggregation = generateAggregation(searchResponse);

        return ProductOpenSearchResult.builder()
                .datas(datas)
                .total(total)
                .page(page)
                .pageSize(req.getPageSize())
                .totalPage(totalPage)
                .next(next)
                .aggregation(aggregation)
                .build();
    }

    private BoolQueryBuilder buildQuery(BoolQueryBuilder boolQuery, ProductOpenSearchRequest req) {

        boolQuery = buildKeywordQuery(boolQuery, req.getKeyword());

        if (StringUtils.isNotEmpty(req.getColor())) {
            boolQuery = boolQuery.must(QueryBuilders.matchQuery(aggregationColor, req.getColor()));
        }

        if (req.getFromPrice() != null && req.getToPrice() != null) {
            boolQuery = boolQuery.must(QueryBuilders.rangeQuery(aggregationPrice).gte(req.getFromPrice()).lte(req.getToPrice()));
        }

        if (Boolean.TRUE.equals(req.getMustHasMarkdown())) {
            boolQuery = boolQuery.must(QueryBuilders.nestedQuery(aggregationTags,
                    QueryBuilders.matchQuery(aggregationTagsName, PRODUCT_TAG_ENUM.MARKDOWN.getTag()), ScoreMode.None));
        }

        if (StringUtils.isNotEmpty(req.getFilterTag())) {
            boolQuery = boolQuery.must(QueryBuilders.nestedQuery(aggregationTags,
                    QueryBuilders.matchQuery(aggregationTagsName, req.getFilterTag()), ScoreMode.None));
        }

        if (req.getTags() != null && !req.getTags().isEmpty()) {
            boolQuery = boolQuery.must(QueryBuilders.nestedQuery(aggregationTags,
                    QueryBuilders.termsQuery(aggregationTagsName, req.getTags()), ScoreMode.None));
        }

        if (req.getCategoryIds() != null && !req.getCategoryIds().isEmpty()) {
            boolQuery = boolQuery.must(QueryBuilders.nestedQuery(aggregationCategory,
                    QueryBuilders.termsQuery(aggregationCategoriesId, req.getCategoryIds()), ScoreMode.None));
        }

        if (req.getSeriesIds() != null && !req.getSeriesIds().isEmpty()) {
            boolQuery = boolQuery.must(QueryBuilders.nestedQuery(aggregationSeries,
                    QueryBuilders.termsQuery(aggregationSeriesId, req.getSeriesIds()), ScoreMode.None));
        }

        if(req.getHasLook() != null) {
            boolQuery = boolQuery.must(QueryBuilders.matchQuery(aggregationHasLook, req.getHasLook()));
        }

        if(req.getLookType() != null) {
            boolQuery = boolQuery.must(QueryBuilders.nestedQuery(aggregationLooks,
                    QueryBuilders.termsQuery(aggregationLooksType, req.getLookType()), ScoreMode.None));
        }

        //is this must need to be confirmed
        if(req.getReportingColorCode()!=null){
            boolQuery=boolQuery.must(QueryBuilders.termsQuery(aggregationReportingColorCode,req.getReportingColorCode()));
        }

        //is this must need to be confirmed
        if(req.getSpec()!=null){
            boolQuery=boolQuery.must(QueryBuilders.termsQuery(aggregationSpec,req.getSpec()));
        }

        boolQuery = buildStockQuery(boolQuery, req.getStockFilter());
        return boolQuery;
    }

    private BoolQueryBuilder buildKeywordQuery(BoolQueryBuilder boolQuery, String keyword) {
        if (StringUtils.isNotEmpty(keyword)) {
            List<ElasticWeightPO> elasticWeights = elasticWeightService.list();

            MultiMatchQueryBuilder textQuery = QueryBuilders.multiMatchQuery( keyword );
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            for (ElasticWeightPO elasticWeightPO : elasticWeights) {
                String fieldName = elasticWeightPO.getName();
                Integer weight = elasticWeightPO.getWeight();
                if(weight == null || weight <= 0) {
                    continue;
                }

                if("text".equals(elasticWeightPO.getType())) {
                    textQuery.field(fieldName, weight);
                }else if("keyword".equals(elasticWeightPO.getType())) {
                    keywordQuery.should(QueryBuilders.matchQuery(fieldName,  keyword).boost(weight));
                }
            }

            BoolQueryBuilder keywordBoolQuery = QueryBuilders.boolQuery();
            // 添加text字段查询条件
            if (textQuery.fields() != null && !textQuery.fields().isEmpty()) {
                keywordBoolQuery.should(textQuery);
            }

            // 添加keyword字段查询条件
            if (!keywordQuery.should().isEmpty()) {
                keywordBoolQuery.should(keywordQuery.minimumShouldMatch(1));
            }

            // 设置至少满足一个条件
            keywordBoolQuery.minimumShouldMatch(1);

            boolQuery = boolQuery.must(keywordBoolQuery);
        }
        return boolQuery;
    }

    private BoolQueryBuilder buildStockQuery(BoolQueryBuilder boolQuery, SearchProductDTO.STOCK_FILTER stockFilter) {
        if (stockFilter == SearchProductDTO.STOCK_FILTER.EC_STOCK) {
            boolQuery = boolQuery.must(QueryBuilders.rangeQuery(sortEcStock).gt(0));
        } else if (stockFilter == SearchProductDTO.STOCK_FILTER.STORE_STOCK) {
            boolQuery = boolQuery.must(QueryBuilders.rangeQuery(sortStoreStock).gt(0));
        } else if (stockFilter == SearchProductDTO.STOCK_FILTER.NATIONWIDE_STOCK) {
            boolQuery = boolQuery.should(QueryBuilders.rangeQuery(sortStoreStock).gt(0))
                    .should(QueryBuilders.rangeQuery(sortOtherStoreStock).gt(0))
                    .minimumShouldMatch(1);
        }
        return boolQuery;
    }

    private ProductOpenSearchResult.Aggregation generateAggregation(SearchResponse searchResponse) {
        ProductOpenSearchResult.Aggregation aggregation = ProductOpenSearchResult.Aggregation.builder()
                .colors(new ArrayList<>())
                .tags(new ArrayList<>())
                .categories(new ArrayList<>())
                .lookIds(new ArrayList<>())
                .series(new ArrayList<>())
                .reportingColors(new ArrayList<>())
                .specs(new ArrayList<>())
                .maxPrice(0)
                .minPrice(0)
                .build();
        if (searchResponse.getAggregations() != null) {

            setColorsAggregation(searchResponse, aggregation);

            setTagsAggregation(searchResponse, aggregation);

            setLooksAggregation(searchResponse, aggregation);

            setSeriesAggregation(searchResponse, aggregation);

            setCategoriesAggregation(searchResponse, aggregation);

            setPriceAggregation(searchResponse, aggregation);

            setReportingColorAggregation(searchResponse, aggregation);

            setSpecsAggregation(searchResponse, aggregation);
        }
        return aggregation;
    }

    private void setColorsAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation) {
        Terms colorTerms = searchResponse.getAggregations().get(aggregationColor);
        if (colorTerms != null) {
            Set<String> colorSet = new HashSet<>();
            for (Terms.Bucket bucket : colorTerms.getBuckets()) {
                String colorCode = bucket.getKeyAsString();
                ParsedStringTerms colorImage = bucket.getAggregations().get(aggregationColorImage);
                for (Terms.Bucket subBucket : colorImage.getBuckets()) {
                    if(colorSet.contains(colorCode)) {
                        continue;
                    }
                    colorSet.add(colorCode);
                    String colorImageUrl = subBucket.getKeyAsString();
                    aggregation.getColors().add(new ProductOpenSearchResult.Color(colorCode, colorImageUrl));
                }
            }
        }
    }

    private void setTagsAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation) {
        ParsedNested parsedNested = searchResponse.getAggregations().get("nested_tags");
        if (parsedNested != null) {
            ParsedStringTerms tagsName = parsedNested.getAggregations().get("tag_name");
            for (Terms.Bucket bucket : tagsName.getBuckets()) {
                aggregation.getTags().add(bucket.getKeyAsString());
            }
        }
    }

    private void setLooksAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation) {
        ParsedNested lookNested = searchResponse.getAggregations().get("nested_looks");
        if (lookNested != null) {
            ParsedStringTerms lookId = lookNested.getAggregations().get("look_id");
            for (Terms.Bucket bucket : lookId.getBuckets()) {
                aggregation.getLookIds().add(bucket.getKeyAsString());
            }
        }
    }

    private void setCategoriesAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation) {
        ParsedNested categoriesNested = searchResponse.getAggregations().get("nested_categories");
        if (categoriesNested != null) {
            ParsedStringTerms categoryIdBuckets = categoriesNested.getAggregations().get("category_id");

            Map<String, String> categoryMap = new HashMap<>();
            for (Terms.Bucket bucket : categoryIdBuckets.getBuckets()) {
                String bucketCategoryId = bucket.getKeyAsString();

                ParsedStringTerms categoryNameBuckets = bucket.getAggregations().get("category_name");
                for (Terms.Bucket subBucket : categoryNameBuckets.getBuckets()) {
                    String categoryName = subBucket.getKeyAsString();
                    categoryMap.put(bucketCategoryId, categoryName);
                }
                ParsedStringTerms categoryParentIdBuckets = bucket.getAggregations().get("category_parentId");
                for (Terms.Bucket subBucket : categoryParentIdBuckets.getBuckets()) {
                    String categoryParentId = subBucket.getKeyAsString();
                    aggregation.getCategories().add(new ProductOpenSearchResult.Category(bucketCategoryId,
                            categoryMap.get(bucketCategoryId), categoryParentId,null));
                }


            }
        }
    }

    private void setSeriesAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation) {
        ParsedNested seriesNested = searchResponse.getAggregations().get("nested_series");
        if (seriesNested != null) {
            ParsedStringTerms seriesIdBuckets = seriesNested.getAggregations().get("series_id");
            for (Terms.Bucket bucket : seriesIdBuckets.getBuckets()) {
                String bucketSeriesId = bucket.getKeyAsString();

                ParsedStringTerms categoryNameBuckets = bucket.getAggregations().get("series_name");
                for (Terms.Bucket subBucket : categoryNameBuckets.getBuckets()) {
                    String seriesName = subBucket.getKeyAsString();
                    aggregation.getSeries().add(new ProductOpenSearchResult.Series(bucketSeriesId, seriesName));
                }
            }
        }
    }

    private void setPriceAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation) {
        ParsedMax maxPriceAgg = searchResponse.getAggregations().get("max_price");
        if (maxPriceAgg != null) {
            aggregation.setMaxPrice(maxPriceAgg.getValue());
        }

        ParsedMin minPriceAgg = searchResponse.getAggregations().get("min_price");
        if (minPriceAgg != null) {
            aggregation.setMinPrice(minPriceAgg.getValue());
        }
    }



    private void setReportingColorAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation){

    List<ProductOpenSearchResult.ReportingColor>  list=  Optional.ofNullable( (Terms) searchResponse.getAggregations().get("reporting_color_code"))
                .map(Terms::getBuckets)
                .orElseGet(ArrayList::new)
                .stream()
                .map(bucket -> {
                    ProductOpenSearchResult.ReportingColor  reportingColor = new ProductOpenSearchResult.ReportingColor();
                    reportingColor.setCode(bucket.getKeyAsString());
                    reportingColor.setName(
                            Optional.ofNullable((Terms)bucket.getAggregations().get("reporting_color_name"))
                                    .map(Terms::getBuckets)
                                    .orElseGet(ArrayList::new)
                                    .stream()
                                    .map(Terms.Bucket::getKeyAsString)
                                    .findAny()
                                    .orElse(null)
                    );
                    reportingColor.setUrl(
                            Optional.ofNullable((Terms)bucket.getAggregations().get("reporting_color_url"))
                                    .map(Terms::getBuckets)
                                    .orElseGet(ArrayList::new)
                                    .stream()
                                    .map(Terms.Bucket::getKeyAsString)
                                    .findAny()
                                    .orElse(null)
                    );

                    return reportingColor;
                })
                .distinct()
                .toList();

    aggregation.setReportingColors(list);

    }



    private void setSpecsAggregation(SearchResponse searchResponse, ProductOpenSearchResult.Aggregation aggregation){

        aggregation.setSpecs(
                Optional.ofNullable((Terms)searchResponse.getAggregations().get("spec"))
                        .map(Terms::getBuckets)
                        .orElseGet(ArrayList::new)
                        .stream()
                        .map(bucket -> {
                            String specUrl=Optional.ofNullable((Terms)bucket.getAggregations().get("spec_url"))
                                    .map(Terms::getBuckets)
                                    .orElseGet(ArrayList::new)
                                    .stream()
                                    .map(Terms.Bucket::getKeyAsString)
                                    .findAny()
                                    .orElse(null);
                            return new ProductOpenSearchResult.Spec(bucket.getKeyAsString(),specUrl);
                        })
                        .distinct()
                        .toList()
        );


    }


}