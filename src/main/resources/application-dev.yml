spring:
  cloud:
    aws:
      credentials:
        access-key: ********************
        secret-key: V15IC/Npu+ChnD+wtuAlo8rLymJlboneDsDPlqmG
      region:
        static: cn-north-1
      secretsmanager:
        enabled: true
        secret-id: paybylink-dev-secret
  datasource:
    url: jdbc:postgresql://${paybylink-dev-secret.database_host_pg}:${paybylink-dev-secret.database_port_pg}/${paybylink-dev-secret.database_name}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&stringtype=unspecified
    username: ${paybylink-dev-secret.database_username_pg}
    password: ${paybylink-dev-secret.database_password_pg}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 3
      minimum-idle: 3
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
  data:
    redis:
      database: ${paybylink-dev-secret.redis_database}
      host: ${paybylink-dev-secret.redis_host}
      port: ${paybylink-dev-secret.redis_port}
      password: ${paybylink-dev-secret.redis_password}
      timeout: 6000
      lettuce:
        pool:
          max-active: 100
          max-wait: -1
          max-idle: 10
          min-idle: 10

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true

wechat:
  jml-agent: ${paybylink-dev-secret.wecom_jml_agentid}
  corps:
    - corpId: ${paybylink-dev-secret.wecom_corpid}
      agents:
        - agentId: ${paybylink-dev-secret.wecom_agentid}
          secret: ${paybylink-dev-secret.wecom_agent_secret}
          token: ${paybylink-dev-secret.wecom_agent_token}
          aesKey: ${paybylink-dev-secret.wecom_agent_aeskey}
        - agentId: ${paybylink-dev-secret.wecom_jml_agentid}
          secret: ${paybylink-dev-secret.wecom_jml_secret}

login:
  default-interceptor-enabled: true
  repeat-login: false
  debug-enabled: true
  expire-second: 1800
  base-back-url: ${paybylink-dev-secret.login_back_url}
  base-front-url: ${paybylink-dev-secret.login_front_url}
  exclude-url-prefix:
    - /product-api/v3/api-docs
    - /product-api/swagger-ui
    - /product-api/webjars

springdoc:
  api-docs:
    path: /product-api/v3/api-docs
    enabled: true
  swagger-ui:
    path: /product-api/swagger-ui
  webjars:
    prefix: /product-api/webjars

knife4j:
  enable: true
  production: false
  basic:
    enable: true
    username: admin
    password: admin

cup-product:
  token-endpoint: ${paybylink-dev-secret.ciam_get_jwt_endpoint}
  client-id: ${paybylink-dev-secret.cup_product_clientid}
  client-secret: ${paybylink-dev-secret.cup_product_client_secret}
  grant-type: client_credentials
  product-endpoint: https://cup-product-preprod.nonprod.cup.burberry.cn/graphql

http:
  client:
    enabled: true
    max-total: 200
    default-max-per-route: 50
    connect-timeout: 10000
    response-timeout: 15000

#d1m Setting
d1m:
  mini-app-id: ${paybylink-dev-secret.ec_mp_appid}
  ec-app-id: ${paybylink-dev-secret.ec_api_appid}
  ec-secret: ${paybylink-dev-secret.ec_api_secret}
  ec-base-Url: https://stg-api-westore.stg.westore.burberry.cn
  code-base-Url: https://stg-api-westore.stg.westore.burberry.cn/
  ec-api:
    token: /api/v4/estore/open/auth/token
    product-list: /api/v4/estore/open/remoteSale/p3/product/list
    product-detail: /api/v4/estore/open/remoteSale/p3/product/detail
    product-share: /api/v4/estore/open/remoteSale/p3/product/share
    cart-share: /api/v4/estore/open/remoteSale/p3/cart/share


logging:
  config: classpath:log4j2.yml

aws:
  open-search:
    username: ${paybylink-dev-secret.open_search_username}
    password: ${paybylink-dev-secret.open_search_password}
    endpoint: ${paybylink-dev-secret.open_search_endpoint}

inner:
  enabled: true
  client-id: ${paybylink-dev-secret.inner_clientid}
  client-secret: ${paybylink-dev-secret.inner_client_secret}
  gateway-server: https://burberry-pay.linkioapp.com
  access-token-url: /common-api/inner-api/v1/getAccessToken