spring:
  cloud:
    aws:
      credentials:
        access-key: ${AWS_SECRETS_MANAGE_UAT_ACCCESS_KEY}
        secret-key: ${AWS_SECRETS_MANAGE_UAT_SECRET_KEY}
      region:
        static: cn-north-1
      secretsmanager:
        enabled: true
        secret-id: paybylink-uat-secret
  datasource:
    url: jdbc:postgresql://${paybylink-uat-secret.database_host_pg}:${paybylink-uat-secret.database_port_pg}/${paybylink-uat-secret.database_name}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&stringtype=unspecified
    username: ${paybylink-uat-secret.database_username}
    password: ${paybylink-uat-secret.database_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 100
      minimum-idle: 20
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
  data:
    redis:
      database: ${paybylink-uat-secret.redis_database}
      host: ${paybylink-uat-secret.redis_host}
      port: ${paybylink-uat-secret.redis_port}
      timeout: 6000
      lettuce:
        pool:
          max-active: 100
          max-wait: -1
          max-idle: 10
          min-idle: 10

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true

wechat:
  jml-agent: ${paybylink-uat-secret.wecom_jml_agentid}
  corps:
    - corpId: ${paybylink-uat-secret.wecom_corpid}
      agents:
        - agentId: ${paybylink-uat-secret.wecom_agentid}
          secret: ${paybylink-uat-secret.wecom_agent_secret}
          token: ${paybylink-uat-secret.wecom_agent_token}
          aesKey: ${paybylink-uat-secret.wecom_agent_aeskey}
        - agentId: ${paybylink-uat-secret.wecom_jml_agentid}
          secret: ${paybylink-uat-secret.wecom_jml_secret}

login:
  default-interceptor-enabled: true
  repeat-login: false
  expire-second: 1800
  base-back-url: ${paybylink-uat-secret.login_back_url}
  base-front-url: ${paybylink-uat-secret.login_front_url}
  exclude-url-prefix:
    - /product-api/v3/api-docs
    - /product-api/swagger-ui
    - /product-api/webjars

springdoc:
  api-docs:
    path: /product-api/v3/api-docs
    enabled: true
  swagger-ui:
    path: /product-api/swagger-ui
  webjars:
    prefix: /product-api/webjars

knife4j:
  enable: true
  production: false
  basic:
    enable: true
    username: admin
    password: admin

cup-product:
  token-endpoint: ${paybylink-uat-secret.ciam_get_jwt_endpoint}
  client-id: ${paybylink-uat-secret.cup_product_clientid}
  client-secret: ${paybylink-uat-secret.cup_product_client_secret}
  grant-type: client_credentials
  product-endpoint: https://cup-product-preprod.nonprod.cup.burberry.cn/graphql

http:
  client:
    enabled: true
    max-total: 200
    default-max-per-route: 50
    connect-timeout: 10000
    response-timeout: 15000

#d1m Setting
d1m:
  mini-app-id: ${paybylink-uat-secret.ec_mp_appid}
  ec-app-id: ${paybylink-uat-secret.ec_api_appid}
  ec-secret: ${paybylink-uat-secret.ec_api_secret}
  ec-base-Url: https://stg-api-westore.stg.westore.burberry.cn
  code-base-Url: https://stg-api-westore.stg.westore.burberry.cn/
  ec-api:
    token: /api/v4/estore/open/auth/token
    product-list: /api/v4/estore/open/remoteSale/p3/product/list
    product-detail: /api/v4/estore/open/remoteSale/p3/product/detail
    product-share: /api/v4/estore/open/remoteSale/p3/product/share
    cart-share: /api/v4/estore/open/remoteSale/p3/cart/share


logging:
  config: classpath:log4j2.yml

aws:
  open-search:
    username: ${paybylink-uat-secret.open_search_username}
    password: ${paybylink-uat-secret.open_search_password}
    endpoint: ${paybylink-uat-secret.open_search_endpoint}

inner:
  enabled: true
  client-id: ${paybylink-uat-secret.inner_clientid}
  client-secret: ${paybylink-uat-secret.inner_client_secret}
  gateway-server: http://service-paybylink-gateway-service.uat.svc.cluster.local:8080
  access-token-url: /common-api/inner-api/v1/getAccessToken