query QueryProductByEan($ean_code: ID!, $language: Language!, $country: CountryCode!) {
  queryProductByEan(ean_code: $ean_code, language: $language, country: $country) {
    id
    name
    colour
    description
    long_description
    material_composition
    limited_availability
    start_date
    end_date
    features
    care_instructions
    materials_names
    country_of_origin
    default_category
    fit
    measurements
    medias {
        code
        type
        url
        metadata {
            photo_type
            sort_order
        }
    }
    model_height
    skus {
        sku_id
        ean
        size_code
        tax_code
    }
    monogrammable
    number_of_colours
    price {
        current {
            currency
            value
        }
        old {
            currency
            value
        }
    }
    product_label
    ready
    related_genders {
        code
        name
    }
    retail_exclusive
    returnable
    short_material
    url
    category_system {
        default_category_tree_branch {
            id
            default_category_id
            start_date
            end_date
            name
            name_en
        }
    }
    primary_image {
        code
        type
        url
    }
    swatch_image {
        code
        type
        url
    }
    colour_alternatives_swatch_info {
        id
        colour
        name
        price {
            current {
                currency
                value
            }
            old {
                currency
                value
            }
        }
        primary_image {
            url
        }
        swatch_image {
            url
        }
    }
  }
}