query QueryStoreStockByLocation($sku_ids: [ID!]!, $location_id: ID!, $paging: PagingInput) {
  queryStoreStockByLocation(sku_ids: $sku_ids, location_id: $location_id, paging: $paging) {
        stocks {
            sku_id
            location_id
            quantity
            last_updated
            source_updated
        }
        paging {
            cursor
            has_next_page
            limit
            total
        }
    }
}