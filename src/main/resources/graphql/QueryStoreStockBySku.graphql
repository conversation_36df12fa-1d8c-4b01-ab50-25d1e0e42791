query QueryStoreStockBySku($sku_id: ID!, $location_ids: [ID!]!, $paging: PagingInput) {
  queryStoreStockBySku(sku_id: $sku_id, location_ids: $location_ids, paging: $paging) {
        stocks {
            sku_id
            location_id
            quantity
            last_updated
            source_updated
        }
        paging {
            cursor
            has_next_page
            limit
            total
        }
    }
}