Configuration:
  status: warn
  Properties:
    Property:
    - name: log.level.console
      value: trace
    - name: log.path
      value: /opt/logs
    - name: project.name
      value: product
  Appenders:
    Console:
      name: CONSOLE
      target: SYSTEM_OUT
      ThresholdFilter:
        level: ${log.level.console}
        onMatch: ACCEPT
        onMismatch: DENY
      PatternLayout:
        pattern: "%d{yyyy-MM-dd HH:mm:ss,SSS}:%5p %20t [%50F:%3L] - %m%n"
    RollingFile:
      name: ROLLING_FILE
      ignoreExceptions: false
      fileName: ${log.path}/${project.name}.log
      filePattern: "${log.path}/$${date:yyyy-MM}/${project.name}-%d{yyyy-MM-dd}-%i.log.gz"
      PatternLayout:
        pattern: "%d{yyyy-MM-dd HH:mm:ss,SSS}:%5p %20t [%50F:%3L] - %m%n"
      Policies:
        SizeBasedTriggeringPolicy:
          size: "10 MB"
      DefaultRolloverStrategy:
        max: 50
  Loggers:
    Root:
      level: info
      AppenderRef:
      - ref: CONSOLE
      - ref: ROLLING_FILE