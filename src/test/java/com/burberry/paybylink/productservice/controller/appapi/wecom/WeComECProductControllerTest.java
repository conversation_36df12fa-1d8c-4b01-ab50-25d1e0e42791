package com.burberry.paybylink.productservice.controller.appapi.wecom;

import com.burberry.paybylink.productservice.model.dto.ec.EcCartShareDTO;
import com.burberry.paybylink.productservice.model.dto.ec.EcProductListDTO;
import com.burberry.paybylink.productservice.model.dto.ec.EcProductShareDTO;
import com.burberry.paybylink.productservice.model.enums.D1M_PEODUCT_SHARE_ENUM;
import com.burberry.paybylink.productservice.model.vo.ECProductItemsVo;
import com.burberry.paybylink.productservice.model.vo.EcShareVo;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.api.base.BaseErrorCode;
import com.yimlink.ymcloud.paybylink.common.model.dto.auth.WeComUserDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 * @Date 2025/5/20 11:08
 */
@SpringBootTest
public class WeComECProductControllerTest {

    @Resource
    private WeComECProductController weComECProductController;

    @Test
    public void testGetEcStock() {
        ApiResponse<ECProductItemsVo> ecStock = weComECProductController.getEcStock("80780071");
        assertNotNull(ecStock);
        assertEquals(ecStock.getCode(), BaseErrorCode.SUCCESS.getCode());
    }

    @Test
    public void testGetEcStocks() {
        EcProductListDTO ecProductListDTO = new EcProductListDTO();
        ecProductListDTO.setCodes(Arrays.asList("80780071", "80772931"));
        ApiResponse<List<ECProductItemsVo>> ecStocks = weComECProductController.getEcStocks(ecProductListDTO);
        assertNotNull(ecStocks);
        assertEquals(ecStocks.getCode(), BaseErrorCode.SUCCESS.getCode());
    }

    @Test
    public void testProductShare() throws UnsupportedEncodingException {
        WeComUserDTO weComUserDTO = new WeComUserDTO();
        weComUserDTO.setGlobalId("111");
        EcProductShareDTO dto = new EcProductShareDTO();
        dto.setStoreId("21");
        dto.setSku("80780071001");
        dto.setType(D1M_PEODUCT_SHARE_ENUM.PRODUCT_LINK);
        ApiResponse<EcShareVo> response = weComECProductController.productShare(weComUserDTO, dto);
        assertNotNull(response);
        assertEquals(response.getCode(), BaseErrorCode.SUCCESS.getCode());
    }

    @Test
    public void testCartShare() throws UnsupportedEncodingException {
        WeComUserDTO weComUserDTO = new WeComUserDTO();
        weComUserDTO.setGlobalId("111");
        EcCartShareDTO dto = new EcCartShareDTO();
        dto.setStoreId("21");
        EcCartShareDTO.CartItemDTO cartItemDTO = new EcCartShareDTO.CartItemDTO();
        cartItemDTO.setCode("80780071");
        cartItemDTO.setSku("80780071001");
        cartItemDTO.setQuantity("1");
        dto.setItems(List.of(cartItemDTO));
        ApiResponse<EcShareVo> response = weComECProductController.cartShare(weComUserDTO, dto);
        assertNotNull(response);
        assertEquals(response.getCode(), BaseErrorCode.SUCCESS.getCode());
    }
}
