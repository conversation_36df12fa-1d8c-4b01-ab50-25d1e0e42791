package com.burberry.paybylink.productservice.controller.appapi.wecom;

import com.burberry.paybylink.productservice.model.vo.ProductVo;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.model.dto.CategoryDTO;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductDTO;
import com.yimlink.ymcloud.paybylink.product.service.ProductBusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class WeComProductControllerTest {

    @Mock
    private ProductBusService productBusService;

    @InjectMocks
    private WeComProductController weComProductController;

    private ProductDTO mockProductDTO;
    private final String TEST_SKU = "test-sku-123";
    private final String TEST_EAN = "test-ean-456";

    @BeforeEach
    public void setUp() {
        // Initialize mocks
        MockitoAnnotations.openMocks(this);

        // Create a mock ProductDTO for testing
        mockProductDTO = new ProductDTO();
        mockProductDTO.setSpu("test-spu-789");
        mockProductDTO.setName("Test Product");
        mockProductDTO.setSku(TEST_SKU);
        mockProductDTO.setEan(TEST_EAN);
        mockProductDTO.setDescription("Test product description");
        mockProductDTO.setColor("Red");
        mockProductDTO.setColorUrl("https://example.com/color/red");
        mockProductDTO.setMaterial("Cotton");
        mockProductDTO.setSize("M");
        
        List<String> images = new ArrayList<>();
        images.add("https://example.com/image1.jpg");
        images.add("https://example.com/image2.jpg");
        mockProductDTO.setImages(images);
        
        mockProductDTO.setMain_image("https://example.com/image1.jpg");
        mockProductDTO.setSmall_main_image("https://example.com/image1_small.jpg");
        mockProductDTO.setPrice(new BigDecimal("100.00"));
        mockProductDTO.setOriginPrice(new BigDecimal("150.00"));
        
        List<CategoryDTO> categories = new ArrayList<>();
        CategoryDTO category = new CategoryDTO();
        category.setCategoryId("cat-1");
        category.setCategoryName("Clothing");
        categories.add(category);
        mockProductDTO.setCategories(categories);
    }

    @Test
    public void testGetBySKU_Success() {
        // Arrange
        when(productBusService.findBySku(TEST_SKU)).thenReturn(mockProductDTO);
        when(productBusService.filterByDate(mockProductDTO)).thenReturn(mockProductDTO);

        // Act
        ApiResponse<ProductVo> response = weComProductController.getBySKU(TEST_SKU);

        // Assert
        assertNotNull(response);
        assertEquals("0", response.getCode());

        
        ProductVo productVo = response.getData();
        assertNotNull(productVo);
        assertEquals(mockProductDTO.getSpu(), productVo.getSpu());
        assertEquals(mockProductDTO.getName(), productVo.getName());
        assertEquals(mockProductDTO.getSku(), productVo.getSku());
        assertEquals(mockProductDTO.getEan(), productVo.getEan());
        assertEquals(mockProductDTO.getDescription(), productVo.getDescription());
        assertEquals(mockProductDTO.getColor(), productVo.getColor());
        assertEquals(mockProductDTO.getColorUrl(), productVo.getColorUrl());
        assertEquals(mockProductDTO.getMaterial(), productVo.getMaterial());
        assertEquals(mockProductDTO.getSize(), productVo.getSize());
        assertEquals(mockProductDTO.getImages(), productVo.getImages());
        assertEquals(mockProductDTO.getMain_image(), productVo.getMain_image());
        assertEquals(mockProductDTO.getSmall_main_image(), productVo.getSmall_main_image());
        assertEquals(mockProductDTO.getPrice(), productVo.getPrice());
        assertEquals(mockProductDTO.getOriginPrice(), productVo.getOriginPrice());
        assertEquals(mockProductDTO.getCategories(), productVo.getCategories());
        
        // Verify that the service method was called exactly once with the correct parameter
        verify(productBusService, times(1)).findBySku(TEST_SKU);
    }

    @Test
    public void testGetByEAN_Success() {
        // Arrange
        when(productBusService.findByEan(TEST_EAN)).thenReturn(mockProductDTO);
        when(productBusService.filterByDate(mockProductDTO)).thenReturn(mockProductDTO);

        // Act
        ApiResponse<ProductVo> response = weComProductController.getByEAN(TEST_EAN);

        // Assert
        assertNotNull(response);
        assertEquals("0", response.getCode());

        
        ProductVo productVo = response.getData();
        assertNotNull(productVo);
        assertEquals(mockProductDTO.getSpu(), productVo.getSpu());
        assertEquals(mockProductDTO.getName(), productVo.getName());
        assertEquals(mockProductDTO.getSku(), productVo.getSku());
        assertEquals(mockProductDTO.getEan(), productVo.getEan());
        assertEquals(mockProductDTO.getDescription(), productVo.getDescription());
        assertEquals(mockProductDTO.getColor(), productVo.getColor());
        assertEquals(mockProductDTO.getColorUrl(), productVo.getColorUrl());
        assertEquals(mockProductDTO.getMaterial(), productVo.getMaterial());
        assertEquals(mockProductDTO.getSize(), productVo.getSize());
        assertEquals(mockProductDTO.getImages(), productVo.getImages());
        assertEquals(mockProductDTO.getMain_image(), productVo.getMain_image());
        assertEquals(mockProductDTO.getSmall_main_image(), productVo.getSmall_main_image());
        assertEquals(mockProductDTO.getPrice(), productVo.getPrice());
        assertEquals(mockProductDTO.getOriginPrice(), productVo.getOriginPrice());
        assertEquals(mockProductDTO.getCategories(), productVo.getCategories());
        
        // Verify that the service method was called exactly once with the correct parameter
        verify(productBusService, times(1)).findByEan(TEST_EAN);
    }

    @Test
    public void testGetBySKU_NullResponse() {
        // Arrange
        when(productBusService.findBySku(TEST_SKU)).thenReturn(null);

        // Act
        ApiResponse<ProductVo> response = weComProductController.getBySKU(TEST_SKU);

        // Assert
        assertNotNull(response);
        assertEquals("0", response.getCode());

        assertEquals(null, response.getData());
        
        // Verify that the service method was called exactly once with the correct parameter
        verify(productBusService, times(1)).findBySku(TEST_SKU);
    }

    @Test
    public void testGetByEAN_NullResponse() {
        // Arrange
        when(productBusService.findByEan(TEST_EAN)).thenReturn(null);

        // Act
        ApiResponse<ProductVo> response = weComProductController.getByEAN(TEST_EAN);

        // Assert
        assertNotNull(response);
        assertEquals("0", response.getCode());

        assertEquals(null, response.getData());
        
        // Verify that the service method was called exactly once with the correct parameter
        verify(productBusService, times(1)).findByEan(TEST_EAN);
    }
    
    @Test
    public void testGetBySKU_EmptyInput() {
        // Arrange
        String emptySku = "";
        when(productBusService.findBySku(emptySku)).thenReturn(null);

        // Act
        ApiResponse<ProductVo> response = weComProductController.getBySKU(emptySku);

        // Assert
        assertNotNull(response);
        assertEquals("0", response.getCode());

        assertNull(response.getData());
        
        // Verify that the service method was called with empty string
        verify(productBusService, times(1)).findBySku(emptySku);
    }
    
    @Test
    public void testGetByEAN_EmptyInput() {
        // Arrange
        String emptyEan = "";
        when(productBusService.findByEan(emptyEan)).thenReturn(null);

        // Act
        ApiResponse<ProductVo> response = weComProductController.getByEAN(emptyEan);

        // Assert
        assertNotNull(response);
        assertEquals("0", response.getCode());

        assertNull(response.getData());
        
        // Verify that the service method was called with empty string
        verify(productBusService, times(1)).findByEan(emptyEan);
    }
    
    @Test
    public void testGetBySKU_ServiceException() {
        // Arrange
        when(productBusService.findBySku(TEST_SKU)).thenThrow(new RuntimeException("Service error"));

        try {
            // Act
            weComProductController.getBySKU(TEST_SKU);
            fail("Expected RuntimeException was not thrown");
        } catch (RuntimeException e) {
            // Assert
            assertEquals("Service error", e.getMessage());
        }
        
        // Verify that the service method was called
        verify(productBusService, times(1)).findBySku(TEST_SKU);
    }
    
    @Test
    public void testGetByEAN_ServiceException() {
        // Arrange
        when(productBusService.findByEan(TEST_EAN)).thenThrow(new RuntimeException("Service error"));

        try {
            // Act
            weComProductController.getByEAN(TEST_EAN);
            fail("Expected RuntimeException was not thrown");
        } catch (RuntimeException e) {
            // Assert
            assertEquals("Service error", e.getMessage());
        }
        
        // Verify that the service method was called
        verify(productBusService, times(1)).findByEan(TEST_EAN);
    }
}
