package com.burberry.paybylink.productservice.controller.innerapi.product;

import com.burberry.paybylink.productservice.model.vo.ProductVo;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.api.base.BaseErrorCode;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductDTO;
import com.yimlink.ymcloud.paybylink.product.service.ProductBusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class InnerProductControllerTest {

    @Mock
    private ProductBusService productBusService;

    @InjectMocks
    private InnerProductController innerProductController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getBySKU_returnsProductVo_whenProductExists() {
        String productSku = "sku123";
        ProductDTO productDTO = new ProductDTO();
        when(productBusService.findBySku(productSku)).thenReturn(productDTO);

        ApiResponse<ProductVo> response = innerProductController.getBySKU(productSku);

        assertNotNull(response);
        assertEquals(BaseErrorCode.SUCCESS.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void getBySKU_returnsError_whenProductNotFound() {
        String productSku = "sku123";
        when(productBusService.findBySku(productSku)).thenReturn(null);

        ApiResponse<ProductVo> response = innerProductController.getBySKU(productSku);

        assertNotNull(response);
        assertNull(response.getData());
    }

}