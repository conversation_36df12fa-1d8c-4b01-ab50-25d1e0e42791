package com.burberry.paybylink.productservice.controller.openapi.health;

import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.api.base.BaseErrorCode;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class HealthControllerTest {

    private final HealthController healthController = new HealthController();

    @Test
    void check_returnsOk_whenCalled() {
        ApiResponse<String> response = healthController.check();

        assertNotNull(response);
        assertEquals("OK", response.getData());
        assertEquals(BaseErrorCode.SUCCESS.getCode(), response.getCode());
    }
}