package com.burberry.paybylink.productservice.service;

import com.burberry.paybylink.productservice.config.CupProductConfig;
import com.burberry.paybylink.productservice.model.ERROR_CODE_ENUM;
import com.burberry.paybylink.productservice.model.dto.cup.*;
import com.yimlink.ymcloud.paybylink.common.api.base.ApiResponse;
import com.yimlink.ymcloud.paybylink.common.exception.ServiceException;
import com.yimlink.ymcloud.paybylink.common.util.RedisUtils;
import com.yimlink.ymcloud.paybylink.common.util.lock.RedisLockerUtil;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CupProductServiceTest {

    @Mock
    private CupProductConfig cupProductConfig;

    @Mock
    private RedisLockerUtil redisLockerUtil;

    @Mock
    private RedisUtils redisUtils;

    @Mock
    private CloseableHttpClient httpClient;

    @Mock
    private CloseableHttpResponse httpResponse;

    @Mock
    private HttpEntity httpEntity;

    @InjectMocks
    private CupProductService cupProductService;

    @BeforeEach
    public void setUp() {
        // Initialize mocks
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(cupProductService, "httpClient", httpClient);
    }


    @Test
    void queryProductById_Success() throws Exception {
        when(cupProductConfig.getProductEndpoint()).thenReturn("http://test-product-endpoint");
        // Prepare test data
        String productId = "test-product-id";
        String mockToken = "mock-access-token";
        String mockGraphQLResponse = "{\"data\":{\"queryProductById\":{\"id\":\"test-product-id\",\"name\":\"Test Product\"}}}";

        // Mock Redis operations
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(mockToken));

        // Mock HTTP response
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(200);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(mockGraphQLResponse.getBytes(StandardCharsets.UTF_8)));
        when(redisUtils.getWithDefaultValue(anyString(), any())).thenReturn(3);

        // Execute test
        CupProductDTO result = cupProductService.queryProductById(productId);

        // Verify
        assertNotNull(result);
        assertEquals("test-product-id", result.getId());
        assertEquals("Test Product", result.getName());
        verify(httpClient, times(1)).execute(any(HttpPost.class));
    }

    @Test
    void queryProductBySku_Success() throws Exception {
        when(cupProductConfig.getProductEndpoint()).thenReturn("http://test-product-endpoint");
        // Prepare test data
        String productId = "test-product-id";
        String mockToken = "mock-access-token";
        String mockGraphQLResponse = "{\"data\":{\"queryProductByEan\":{\"id\":\"test-product-id\",\"name\":\"Test Product\"}}}";

        // Mock Redis operations
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(mockToken));

        // Mock HTTP response
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(200);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(mockGraphQLResponse.getBytes(StandardCharsets.UTF_8)));
        when(redisUtils.getWithDefaultValue(anyString(), any())).thenReturn(3);

        // Execute test
        CupProductDTO result = cupProductService.queryProductByEan(productId);

        // Verify
        assertNotNull(result);
        assertEquals("test-product-id", result.getId());
        assertEquals("Test Product", result.getName());
        verify(httpClient, times(1)).execute(any(HttpPost.class));
    }

    @Test
    void queryProductByEan_Success() throws Exception {
        when(cupProductConfig.getProductEndpoint()).thenReturn("http://test-product-endpoint");
        // Prepare test data
        String productId = "test-product-id";
        String mockToken = "mock-access-token";
        String mockGraphQLResponse = "{\"data\":{\"queryProductBySku\":{\"id\":\"test-product-id\",\"name\":\"Test Product\"}}}";

        // Mock Redis operations
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(mockToken));

        // Mock HTTP response
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(200);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(mockGraphQLResponse.getBytes(StandardCharsets.UTF_8)));
        when(redisUtils.getWithDefaultValue(anyString(), any())).thenReturn(3);

        // Execute test
        CupProductDTO result = cupProductService.queryProductBySku(productId);

        // Verify
        assertNotNull(result);
        assertEquals("test-product-id", result.getId());
        assertEquals("Test Product", result.getName());
        verify(httpClient, times(1)).execute(any(HttpPost.class));
    }

    @Test
    void queryProductById_NotFound() throws Exception {
        when(cupProductConfig.getProductEndpoint()).thenReturn("http://test-product-endpoint");
        // Prepare test data
        String productId = "non-existent-id";
        String mockToken = "mock-access-token";
        String mockGraphQLResponse = "{\"errors\":[{\"message\":\"Product not found\"}]}";

        // Mock Redis operations
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(mockToken));

        // Mock HTTP response
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(200);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(mockGraphQLResponse.getBytes(StandardCharsets.UTF_8)));
        when(redisUtils.getWithDefaultValue(anyString(), any())).thenReturn(3);

        // Execute and verify
        ServiceException exception = assertThrows(ServiceException.class, () ->
            cupProductService.queryProductById(productId));
        assertEquals(ERROR_CODE_ENUM.CUP_PRODUCT_NOT_EXIST.getCode(), exception.getErrorCode());
    }

    @Test
    void queryProductById_Unauthorized() throws Exception {
        when(cupProductConfig.getProductEndpoint()).thenReturn("http://test-product-endpoint");
        // Prepare test data
        String productId = "test-product-id";
        String mockToken = "mock-access-token";

        // Mock Redis operations
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(mockToken));

        // Mock HTTP response with 401
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(401);
        when(redisUtils.getWithDefaultValue(anyString(), any())).thenReturn(3);

        // Execute and verify
        ServiceException exception = assertThrows(ServiceException.class, () ->
            cupProductService.queryProductById(productId));
        assertEquals(ERROR_CODE_ENUM.CUP_API_ERROR.getCode(), exception.getErrorCode());
    }


    @Test
    void getAccessTokenInner_Success() throws Exception {
        // Arrange
        String key = "test:key";
        String mockToken = "mock-access-token";

        when(redisUtils.get(key)).thenReturn(null);
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(mockToken));

        // Act
        ApiResponse<String> response = cupProductService.getAccessTokenInner(key, false);

        // Assert
        assertTrue(response.isSuccess());
        assertEquals(mockToken, response.getData());
    }

    @Test
    void getAccessTokenInner_ExistingToken() throws IOException {
        // Arrange
        String key = "test:key";
        String existingToken = "existing-token";

        when(redisUtils.get(key)).thenReturn(existingToken);

        // Act
        ApiResponse<String> response = cupProductService.getAccessTokenInner(key, false);

        // Assert
        assertTrue(response.isSuccess());
        assertEquals(existingToken, response.getData());
        verify(httpClient, never()).execute(any(HttpPost.class));
    }

    @Test
    void getAccessTokenInner_ForceRefresh() throws Exception {
        // Arrange
        String key = "test:key";
        String existingToken = "existing-token";
        String newToken = "new-token";
        when(redisUtils.get(key)).thenReturn(existingToken);
        when(redisLockerUtil.tryLock(any(), any(), anyString(), anyLong(), anyLong()))
                .thenReturn(ApiResponse.success(newToken));
        // Act
        ApiResponse<String> response = cupProductService.getAccessTokenInner(key, true);

        // Assert
        assertTrue(response.isSuccess());
        assertEquals(newToken, response.getData());
    }

    @Test
    void refreshAccessToken_returnsToken_whenRequestIsSuccessful() throws Exception {
        String key = "test-key";
        String basicToken = "Basic mock-basic-token";
        String mockResponse = "{\"token\":\"mock-token\",\"expires_in\":3600}";
        when(cupProductConfig.getClientId()).thenReturn("mock-client-id");
        when(cupProductConfig.getClientSecret()).thenReturn("mock-client-secret");
        when(cupProductConfig.getTokenEndpoint()).thenReturn("http://mock-token-endpoint");
        when(cupProductConfig.getGrantType()).thenReturn("client_credentials");

        HttpPost httpPost = new HttpPost("http://mock-token-endpoint");
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(200);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(mockResponse.getBytes(StandardCharsets.UTF_8)));

        ApiResponse<String> response = cupProductService.refreshAccessToken(key);

        assertTrue(response.isSuccess());
        assertEquals("mock-token", response.getData());
        verify(redisUtils, times(1)).set(eq(key), eq("mock-token"), eq(3300L));
    }

    @Test
    void refreshAccessToken_throwsException_whenResponseHasErrorMessage() throws Exception {
        String key = "test-key";
        String mockResponse = "{\"message\":\"Invalid credentials\"}";
        when(cupProductConfig.getClientId()).thenReturn("mock-client-id");
        when(cupProductConfig.getClientSecret()).thenReturn("mock-client-secret");
        when(cupProductConfig.getTokenEndpoint()).thenReturn("http://mock-token-endpoint");

        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(200);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(mockResponse.getBytes(StandardCharsets.UTF_8)));

        ApiResponse<String> apiResponse = cupProductService.refreshAccessToken(key);

        assertEquals(ERROR_CODE_ENUM.CUP_API_ERROR.getCode(), apiResponse.getCode());
        verify(redisUtils, never()).set(anyString(), anyString(), anyLong());
    }

    @Test
    void refreshAccessToken_returnsError_whenResponseStatusIsNotSuccessful() throws Exception {
        String key = "test-key";
        when(cupProductConfig.getTokenEndpoint()).thenReturn("http://mock-token-endpoint");

        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getCode()).thenReturn(500);

        ApiResponse<String> response = cupProductService.refreshAccessToken(key);

        assertFalse(response.isSuccess());
        assertEquals(ERROR_CODE_ENUM.CUP_ACCESS_TOKEN_FAILED.getCode(), response.getCode());
        verify(redisUtils, never()).set(anyString(), anyString(), anyLong());
    }

    @Test
    void refreshAccessToken_returnsError_whenHttpClientThrowsException() throws Exception {
        String key = "test-key";
        when(cupProductConfig.getTokenEndpoint()).thenReturn("http://mock-token-endpoint");

        when(httpClient.execute(any(HttpPost.class))).thenThrow(new IOException("Mock IO Exception"));

        ApiResponse<String> response = cupProductService.refreshAccessToken(key);

        assertFalse(response.isSuccess());
        assertEquals(ERROR_CODE_ENUM.CUP_ACCESS_TOKEN_FAILED.getCode(), response.getCode());
        verify(redisUtils, never()).set(anyString(), anyString(), anyLong());
    }


}