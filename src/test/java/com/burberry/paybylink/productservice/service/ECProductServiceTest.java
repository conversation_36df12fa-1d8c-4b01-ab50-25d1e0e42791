package com.burberry.paybylink.productservice.service;

import com.burberry.paybylink.productservice.model.dto.ec.EcCartShareDTO;
import com.burberry.paybylink.productservice.model.dto.ec.EcProductShareDTO;
import com.burberry.paybylink.productservice.model.enums.D1M_PEODUCT_SHARE_ENUM;
import com.burberry.paybylink.productservice.model.vo.ECProductItemsVo;
import com.burberry.paybylink.productservice.model.vo.EcShareVo;
import com.yimlink.ymcloud.paybylink.common.model.dto.auth.WeComUserDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Date 2025/5/20 13:37
 */
@SpringBootTest
public class ECProductServiceTest {

    @Resource
    private ECProductService ecProductService;

    @Test
    public void testGetEcStock() {
        ECProductItemsVo ecStock = ecProductService.getEcStock("80780071");
        assertNotNull(ecStock);
    }

    @Test
    public void testGetEcStocks() {
        List<ECProductItemsVo> ecStocks = ecProductService.getEcStocks(Arrays.asList("80780071", "80772931"));
        assertNotNull(ecStocks);
        assertFalse(ecStocks.isEmpty());
    }

    @Test
    public void testProductShare() throws UnsupportedEncodingException {
        WeComUserDTO weComUserDTO = new WeComUserDTO();
        weComUserDTO.setGlobalId("111");
        EcProductShareDTO dto = new EcProductShareDTO();
        dto.setStoreId("21");
        dto.setSku("80780071001");
        dto.setType(D1M_PEODUCT_SHARE_ENUM.PRODUCT_LINK);
        EcShareVo result = ecProductService.productShare(weComUserDTO, dto);
        assertNotNull(result);
    }

    @Test
    public void testCartShare() throws UnsupportedEncodingException {
        WeComUserDTO weComUserDTO = new WeComUserDTO();
        weComUserDTO.setGlobalId("111");
        EcCartShareDTO dto = new EcCartShareDTO();
        dto.setStoreId("21");
        EcCartShareDTO.CartItemDTO cartItemDTO = new EcCartShareDTO.CartItemDTO();
        cartItemDTO.setCode("80780071");
        cartItemDTO.setSku("80780071001");
        cartItemDTO.setQuantity("1");
        dto.setItems(List.of(cartItemDTO));
        EcShareVo result = ecProductService.cartShare(weComUserDTO, dto);
        assertNotNull(result);
    }
}
