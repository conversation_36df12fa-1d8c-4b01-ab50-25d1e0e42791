package com.burberry.paybylink.productservice.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burberry.paybylink.productservice.opensearch.OpenSearchService;
import com.yimlink.ymcloud.paybylink.common.model.enums.STATUS_ENUM;
import com.yimlink.ymcloud.paybylink.common.model.po.ElasticPO;
import com.yimlink.ymcloud.paybylink.common.service.IElasticService;
import com.yimlink.ymcloud.paybylink.common.util.DateUtils;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.ProductOpenSearchDTO;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.ProductOpenSearchRequest;
import com.yimlink.ymcloud.paybylink.product.model.dto.opensearch.ProductOpenSearchResult;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest
public class OpenSearchServiceTest2 {

    @Resource
    private OpenSearchService openSearchService;
    @Resource
    private IElasticService elasticService;

//    @Test
    public void testIndex() throws IOException, InterruptedException {
//        openSearchService.deleteIndex();
//        openSearchService.createProductIndex();

//        ProductOpenSearchResult result = openSearchService.searchProduct(ProductOpenSearchRequest.builder()
//                .keyword("大衣")
//                .storeCode("9030")
//                .build()
//        );
//        System.out.println(result);
    }
}
