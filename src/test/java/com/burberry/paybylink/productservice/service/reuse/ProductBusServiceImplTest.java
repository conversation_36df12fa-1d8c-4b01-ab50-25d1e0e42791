package com.burberry.paybylink.productservice.service.reuse;

import com.burberry.paybylink.productservice.model.dto.cup.CupProductDTO;
import com.yimlink.ymcloud.paybylink.common.exception.ServiceException;
import com.yimlink.ymcloud.paybylink.common.model.dto.ProductDTO;
import com.burberry.paybylink.productservice.service.CupProductService;
import com.yimlink.ymcloud.paybylink.common.model.po.ProductPO;
import com.yimlink.ymcloud.paybylink.common.repository.mapper.ProductMapper;
import org.checkerframework.checker.units.qual.C;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ProductBusServiceImplTest {

    @Mock
    private CupProductService cupProductService;

    @Mock
    private ProductMapper productMapper;

    @InjectMocks
    private ProductBusServiceImpl productBusServiceImpl;

    private CupProductDTO cupProductDTO;
    private final String sku = "sku123";
    private final String ean = "ean123";
    private final String spu = "spu123";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        CupProductDTO.Sku sku1 = new CupProductDTO.Sku();
        sku1.setSkuId(sku);
        sku1.setEan(ean);
        CupProductDTO.Category category = new CupProductDTO.Category();
        category.setDefault_category_id("categoryId");
        category.setId("categoryId");
        category.setName("categoryName");
        CupProductDTO.Media media = new CupProductDTO.Media();
        media.setUrl("https://");
        CupProductDTO.CategorySystem categorySystem = new CupProductDTO.CategorySystem();
        categorySystem.setDefault_category_tree_branch(Collections.singletonList(category));
        CupProductDTO.Price price = new CupProductDTO.Price();
        price.setCurrent(new CupProductDTO.CurrentPrice());
        price.setOld(new CupProductDTO.OldPrice());
        cupProductDTO = new CupProductDTO();
        cupProductDTO.setSkus(List.of(sku1));
        cupProductDTO.setCategory_system(categorySystem);
        cupProductDTO.setMedias(Collections.singletonList(media));
        cupProductDTO.setSwatch_image(media);
        cupProductDTO.setPrice(price);
        cupProductDTO.setId(spu);
    }

    @Test
    void findBySku_returnsProductDTO_whenSkuExists() {

        when(cupProductService.queryProductBySku(sku)).thenReturn(cupProductDTO);

        ProductDTO result = productBusServiceImpl.findBySku(sku);

        assertNotNull(result);
        assertEquals(sku, result.getSku());
    }

    @Test
    void findByEan_returnsProductDTO_whenEanExists() {
        when(cupProductService.queryProductByEan(ean)).thenReturn(cupProductDTO);

        ProductDTO result = productBusServiceImpl.findByEan(ean);

        assertNotNull(result);
        assertEquals(ean, result.getEan());
    }

    @Test
    void findBySpu_returnsProductDTOList_whenSpuExists() {
        when(cupProductService.queryProductById(spu)).thenReturn(cupProductDTO);

        List<ProductDTO> result = productBusServiceImpl.findBySpu(spu);

        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

}